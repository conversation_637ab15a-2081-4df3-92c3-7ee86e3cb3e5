"use client"

import * as React from "react"
import { CalendarIcon, X, Clock, ChevronDown } from "lucide-react"
import { addDays, format } from "date-fns"
import type { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

interface DateRangePickerProps {
  className?: string
  onChange?: (dateRange: DateRange | undefined) => void
  placeholder?: string
  value?: DateRange | undefined
}

export function DateRangePicker({ 
  className, 
  onChange, 
  placeholder = "Pick a date range",
  value 
}: DateRangePickerProps) {
  const [date, setDate] = React.useState<DateRange | undefined>(value)
  const [open, setOpen] = React.useState(false)
  const [selectedPreset, setSelectedPreset] = React.useState<string>("")

  // Update internal state when value prop changes
  React.useEffect(() => {
    setDate(value)
  }, [value])

  // Predefined date ranges with better organization
  const quickOptions = [
    { value: "today", label: "Today", icon: Clock },
    { value: "yesterday", label: "Yesterday", icon: Clock },
    { value: "7days", label: "Last 7 days", icon: Clock },
    { value: "30days", label: "Last 30 days", icon: Clock },
  ]

  const periodOptions = [
    { value: "thisMonth", label: "This month", icon: CalendarIcon },
    { value: "lastMonth", label: "Last month", icon: CalendarIcon },
    { value: "thisYear", label: "This year", icon: CalendarIcon },
  ]

  // Handle preset selection
  const handleRangeSelect = (value: string) => {
    const today = new Date()
    let newRange: DateRange | undefined

    switch (value) {
      case "today":
        newRange = { from: today, to: today }
        break
      case "yesterday":
        const yesterday = addDays(today, -1)
        newRange = { from: yesterday, to: yesterday }
        break
      case "7days":
        newRange = { from: addDays(today, -6), to: today }
        break
      case "30days":
        newRange = { from: addDays(today, -29), to: today }
        break
      case "thisMonth":
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
        const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0)
        newRange = { from: firstDayOfMonth, to: lastDayOfMonth }
        break
      case "lastMonth":
        const firstDayOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
        const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0)
        newRange = { from: firstDayOfLastMonth, to: lastDayOfLastMonth }
        break
      case "thisYear":
        const firstDayOfYear = new Date(today.getFullYear(), 0, 1)
        const lastDayOfYear = new Date(today.getFullYear(), 11, 31)
        newRange = { from: firstDayOfYear, to: lastDayOfYear }
        break
      default:
        return
    }

    setDate(newRange)
    setSelectedPreset(value)
    onChange?.(newRange)
  }

  // Handle calendar date change
  const handleDateChange = (newDate: DateRange | undefined) => {
    setDate(newDate)
    setSelectedPreset("") // Clear preset when manually selecting dates
    onChange?.(newDate)
  }

  // Clear date range
  const clearDateRange = () => {
    setDate(undefined)
    setSelectedPreset("")
    onChange?.(undefined)
    setOpen(false)
  }

  // Format display text
  const getDisplayText = () => {
    if (!date?.from) return placeholder
    
    if (selectedPreset) {
      const preset = [...quickOptions, ...periodOptions].find(p => p.value === selectedPreset)
      return preset?.label || placeholder
    }

    if (date.to) {
      return `${format(date.from, "MMM dd, y")} - ${format(date.to, "MMM dd, y")}`
    }
    
    return format(date.from, "MMM dd, y")
  }

  return (
    <div className={cn("relative", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full min-w-[280px] justify-between text-left font-normal h-10 px-3 py-2",
              !date?.from && "text-muted-foreground"
            )}
          >
            <div className="flex items-center flex-1 min-w-0">
              <CalendarIcon className="mr-2 h-4 w-4 shrink-0" />
              <span className="truncate">{getDisplayText()}</span>
            </div>
            <div className="flex items-center ml-2 shrink-0">
              {date?.from && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 text-muted-foreground hover:text-foreground mr-1"
                  onClick={(e) => {
                    e.stopPropagation()
                    clearDateRange()
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
              <ChevronDown className="h-4 w-4 opacity-50" />
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0 max-w-none" align="start">
          <div className="flex">
            {/* Quick Presets Sidebar */}
            <div className="w-48 p-3 border-r bg-muted/20">
              <div className="space-y-4">
                <div>
                  <h4 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3">
                    Quick Select
                  </h4>
                  <div className="space-y-1">
                    {quickOptions.map((option) => {
                      const Icon = option.icon
                      return (
                        <Button
                          key={option.value}
                          variant={selectedPreset === option.value ? "secondary" : "ghost"}
                          size="sm"
                          className="w-full justify-start text-sm h-8 px-2"
                          onClick={() => handleRangeSelect(option.value)}
                        >
                          <Icon className="mr-2 h-3 w-3" />
                          {option.label}
                        </Button>
                      )
                    })}
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3">
                    Periods
                  </h4>
                  <div className="space-y-1">
                    {periodOptions.map((option) => {
                      const Icon = option.icon
                      return (
                        <Button
                          key={option.value}
                          variant={selectedPreset === option.value ? "secondary" : "ghost"}
                          size="sm"
                          className="w-full justify-start text-sm h-8 px-2"
                          onClick={() => handleRangeSelect(option.value)}
                        >
                          <Icon className="mr-2 h-3 w-3" />
                          {option.label}
                        </Button>
                      )
                    })}
                  </div>
                </div>
              </div>
            </div>
            
            {/* Calendar */}
            <div className="p-3">
              <Calendar
                mode="range"
                defaultMonth={date?.from}
                selected={date}
                onSelect={handleDateChange}
                numberOfMonths={2}
                className="rounded-md border-0"
                classNames={{
                  months: "flex flex-row space-x-4",
                  month: "space-y-4",
                  caption: "flex justify-center pt-1 relative items-center",
                  caption_label: "text-sm font-medium",
                  nav: "space-x-1 flex items-center",
                  nav_button: "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",
                  nav_button_previous: "absolute left-1",
                  nav_button_next: "absolute right-1",
                  table: "w-full border-collapse space-y-1",
                  head_row: "flex",
                  head_cell: "text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",
                  row: "flex w-full mt-2",
                  cell: "text-center text-sm p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
                  day: "h-8 w-8 p-0 font-normal aria-selected:opacity-100",
                  day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
                  day_today: "bg-accent text-accent-foreground",
                  day_outside: "text-muted-foreground opacity-50",
                  day_disabled: "text-muted-foreground opacity-50",
                  day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
                  day_hidden: "invisible",
                }}
              />
            </div>
          </div>
          
          {/* Footer */}
          {date?.from && (
            <div className="p-3 border-t bg-muted/20">
              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">
                  {date.to 
                    ? `${format(date.from, "MMM dd, yyyy")} - ${format(date.to, "MMM dd, yyyy")}`
                    : format(date.from, "MMM dd, yyyy")
                  }
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearDateRange}
                  className="h-7"
                >
                  Clear
                </Button>
              </div>
            </div>
          )}
        </PopoverContent>
      </Popover>
    </div>
  )
}