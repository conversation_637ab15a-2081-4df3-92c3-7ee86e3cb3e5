import prisma from '../config/database';
import { AppError } from '../utils/errorHandler';
import { createNotification } from './notification.service';
import { notifyEnquirySubmitted } from './adminNotification.service';
import { sendEmail } from './email.service';
import { ContactPreference, Enquiry, EnquiryStatus, NotificationType } from '@prisma/client';
import { generateEnquiryNumber } from '../utils/enquiryNumber';

/**
 * Get user's enquiries with pagination and filtering
 * @param userId - User ID
 * @param options - Query options for filtering and pagination
 * @returns Paginated enquiries
 */
export const getUserEnquiries = async (
  userId: string,
  options: {
    page?: number;
    limit?: number;
    status?: EnquiryStatus;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }
) => {
  const {
    page = 1,
    limit = 10,
    status,
    sortBy = 'createdAt',
    sortOrder = 'desc',
  } = options;

  const skip = (page - 1) * limit;

  // Build where clause
  const where: any = { userId };
  if (status) where.status = status;

  // Build order by
  const orderBy: any = {};
  orderBy[sortBy] = sortOrder;

  // Get enquiries with pagination
  const [enquiries, total] = await Promise.all([
    prisma.enquiry.findMany({
      where,
      orderBy,
      skip,
      take: limit,
    }),
    prisma.enquiry.count({ where }),
  ]);

  // Calculate pagination info
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    enquiries,
    pagination: {
      total,
      page,
      limit,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
  };
};

/**
 * Get all enquiries (admin only) with pagination and filtering
 * @param options - Query options for filtering and pagination
 * @returns Paginated enquiries
 */
export const getAllEnquiries = async (
  options: {
    page?: number;
    limit?: number;
    status?: EnquiryStatus;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }
) => {
  const {
    page = 1,
    limit = 10,
    status,
    sortBy = 'createdAt',
    sortOrder = 'desc',
  } = options;

  const skip = (page - 1) * limit;

  // Build where clause
  const where: any = {};
  if (status) where.status = status;

  // Build order by
  const orderBy: any = {};
  orderBy[sortBy] = sortOrder;

  // Get enquiries with pagination
  const [enquiries, total] = await Promise.all([
    prisma.enquiry.findMany({
      where,
      orderBy,
      skip,
      take: limit,
      include: {
        rider: {
          select: {
            id: true,
            gstNumber: true,
            companyName: true,
            contactPerson: true,
            email: true,
            contactNumber: true,
            paymentTerms: true,
            isApproved: true,
            approvedAt: true,
            createdAt: true,
          },
        },
      },
    }),
    prisma.enquiry.count({ where }),
  ]);

  // Calculate pagination info
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    enquiries,
    pagination: {
      total,
      page,
      limit,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
  };
};

/**
 * Get enquiry by ID
 * @param enquiryId - Enquiry ID
 * @param userId - User ID (optional, for authorization)
 * @returns Enquiry
 */
export const getEnquiryById = async (enquiryId: string, userId?: string): Promise<Enquiry> => {
  const enquiry = await prisma.enquiry.findUnique({
    where: { id: enquiryId },
    include: {
      rider: {
        select: {
          id: true,
          gstNumber: true,
          companyName: true,
          contactPerson: true,
          email: true,
          contactNumber: true,
          paymentTerms: true,
          isApproved: true,
          approvedAt: true,
          createdAt: true,
        },
      },
    },
  });

  if (!enquiry) {
    throw new AppError('Enquiry not found', 404);
  }

  // Check if user is authorized to view this enquiry
  if (userId && enquiry.userId !== userId) {
    throw new AppError('You are not authorized to view this enquiry', 403);
  }

  return enquiry;
};

/**
 * Create new enquiry
 * @param userId - User ID
 * @param data - Enquiry data
 * @returns Created enquiry
 */
export const createEnquiry = async (
  userId: string,
  data: {
    type : string;
    gsm : string;
    bf : string;
    width: string;
    quantity : number;
    message?: string;
    contactPreference: ContactPreference;
  }
): Promise<Enquiry> => {
  // Get rider details for notification
  const rider = await prisma.rider.findUnique({
    where: { id: userId },
    select: {
      companyName: true,
      contactPerson: true,
      email: true,
      contactNumber: true,
    },
  });

  if (!rider) {
    throw new AppError('User not found', 404);
  }

  // Generate enquiry number
  const enquiryNumber = await generateEnquiryNumber();

  // Create enquiry
  const enquiry = await prisma.enquiry.create({
    data: {
      enquiryNumber,
      userId,
      type: data.type,
      gsm: data.gsm,
      bf: data.bf,
      width: data.width,
      quantity: data.quantity,
      message: data.message? data.message : null,
      contactPreference: data.contactPreference,
    },
  });

  // Create notification for user
  await createNotification(
    userId,
    'ENQUIRY_RESPONSE',
    'Enquiry Submitted',
    `Your enquiry #${enquiryNumber} has been submitted successfully.`
  );

  // Create admin notification for new enquiry using the new admin notification system
  try {
    await notifyEnquirySubmitted({
      id: enquiry.id,
      companyName: rider.companyName,
      productName: `${data.type} ${data.gsm}GSM`,
      userId: userId,
    });
  } catch (adminNotificationError) {
    console.error('Failed to create admin notification for enquiry submission:', adminNotificationError);
    // Don't fail the main operation if admin notification fails
  }

  try {
      await sendEmail(
        rider.email,
        `New Enquiry #${enquiryNumber} Received`,
        `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>New Enquiry Received</h2>
            <p>A new enquiry has been received from ${rider.companyName}.</p>
            <p><strong>Enquiry Details:</strong></p>
            <p><strong>Type:</strong> ${enquiry.type}</p>
            <p><strong>GSM:</strong> ${enquiry.gsm}</p>
            <p><strong>BF:</strong> ${enquiry.bf}</p>
            <p><strong>Width:</strong> ${enquiry.width}</p>
            <p><strong>Quantity:</strong> ${enquiry.quantity}</p>
            <p><strong>Message:</strong> ${enquiry.message}</p>
            <p>Please log in to the admin dashboard to view and respond to this enquiry.</p>
          </div>
        `
        );
        console.log('Email sent to rider:', rider.email);
    } catch (error) {
      console.error('Error sending enquiry response email:', error);
      // Continue even if email sending fails
    }

  return enquiry;
};

/**
 * Close an enquiry
 * @param enquiryId - Enquiry ID
 * @param userId - User ID (for authorization)
 * @returns Closed enquiry
 */
export const closeEnquiry = async (enquiryId: string, userId: string): Promise<Enquiry> => {
  // Get enquiry and check ownership
  const enquiry = await getEnquiryById(enquiryId, userId);

  // Check if enquiry can be closed
  if (enquiry.status === EnquiryStatus.CLOSED) {
    throw new AppError('Enquiry is already closed', 400);
  }

  // Close enquiry
  const closedEnquiry = await prisma.enquiry.update({
    where: { id: enquiryId },
    data: {
      status: EnquiryStatus.CLOSED,
    },
  });

  // Create notification for user
  await createNotification(
    userId,
    'ENQUIRY_RESPONSE',
    'Enquiry Closed',
    `Your enquiry #${enquiry.enquiryNumber} has been closed.`
  );

  return closedEnquiry;
};

/**
 * Respond to an enquiry (admin only)
 * @param enquiryId - Enquiry ID
 * @param response - Response message
 * @returns Updated enquiry
 */
export const respondToEnquiry = async (
  enquiryId: string,
  response: string
): Promise<Enquiry> => {
  // Get enquiry
  const enquiry = await prisma.enquiry.findUnique({
    where: { id: enquiryId },
    include: {
      rider: {
        select: {
          id: true,
          email: true,
          contactNumber: true,
          companyName: true,
          contactPerson: true,
        },
      },
    },
  });

  if (!enquiry) {
    throw new AppError('Enquiry not found', 404);
  }

  // Check if enquiry can be responded to
  if (enquiry.status === EnquiryStatus.CLOSED) {
    throw new AppError('Cannot respond to a closed enquiry', 400);
  }

  // Update enquiry
  const updatedEnquiry = await prisma.enquiry.update({
    where: { id: enquiryId },
    data: {
      response,
      status: EnquiryStatus.RESPONDED,
      respondedAt: new Date(),
    },
  });

  // Create notification for user
  await createNotification(
    enquiry.userId,
    'ENQUIRY_RESPONSE',
    'Enquiry Response Received',
    `Your enquiry #${enquiry.enquiryNumber} has received a response.`
  );
  
    try {
      await sendEmail(
        enquiry.rider.email,
        `Response to Your Enquiry #${enquiry.enquiryNumber}`,
        `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>Response to Your Enquiry</h2>
            <p>Dear ${enquiry.rider.contactPerson},</p>
            <p>We have responded to your enquiry #${enquiry.enquiryNumber} regarding type: ${enquiry.type} </br> gsm: ${enquiry.gsm} </br> bf: ${enquiry.bf} </br> width: ${enquiry.width} </br> quantity: ${enquiry.quantity}.</p>
            <div style="background-color: #f4f4f4; padding: 15px; margin: 15px 0; border-left: 4px solid #4CAF50;">
              <p><strong>Our Response:</strong></p>
              <p>${response}</p>
            </div>
            <p>If you have any further questions, please feel free to reply to this email or create a new enquiry.</p>
            <p>Thank you for choosing Kraft Paper App for your business needs.</p>
          </div>
        `
      );
    } catch (error) {
      console.error('Error sending enquiry response email:', error);
      // Continue even if email sending fails
    }

  return updatedEnquiry;
};
